.github-sponsors-page {
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.github-sponsors-page a {
  text-decoration: none;
}

.github-sponsors-page p {
  margin: 1rem 0;
  line-height: 1.6;
}

.sponsors-section {
  margin: 3rem 0;
}

.section-header {
  text-align: center;
  position: relative;
  margin: 3rem 0 2rem 0;
  font-weight: 600;
  font-size: 2.5rem;
}

.section-header::before,
.section-header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: calc(50% - 120px);
  height: 1px;
  background: #e2e8f0;
}

.section-header::before {
  left: 0;
}

.section-header::after {
  right: 0;
}

.section-title {
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.sponsors-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 2rem 0;
}

.sponsor-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sponsor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.sponsor-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.sponsor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 0.5rem;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.25rem;
  color: #2563eb;
  object-fit: cover;
}

.sponsor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}

.sponsor-info h3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.sponsor-info h3 a {
  color: inherit;
  text-decoration: none;
}

.sponsor-info h3 a:hover {
  color: #2563eb;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .section-header::before,
  .section-header::after {
    width: calc(50% - 80px);
  }

  .sponsor-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (prefers-color-scheme: dark) {
  .sponsor-card {
    background: #1a1a1a;
    border-color: rgba(255,255,255,0.1);
  }

  .section-header::before,
  .section-header::after {
    background: rgba(255,255,255,0.1);
  }

  .sponsor-info h3,
  .sponsor-info h3 a {
    color: #ffffff;
  }

  .sponsor-info h3 a:hover {
    color: #60a5fa;
  }
}
