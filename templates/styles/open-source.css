.open-source-portfolio {
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.open-source-portfolio a {
  text-decoration: none;
}

/* GitHub Sponsors CTA Section */
.sponsors-cta {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  margin: 2rem 0 3rem 0;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.sponsors-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.1)">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cta-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.95;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.cta-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.button-icon {
  font-size: 1.2rem;
}

.portfolio-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  margin: 3rem 0;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-item {
  position: relative;
}

.stat-item::before {
  content: "📊";
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

.stat-item:nth-child(1)::before { content: "💻"; }
.stat-item:nth-child(2)::before { content: "📦"; }
.stat-item:nth-child(3)::before { content: "🏢"; }
.stat-item:nth-child(4)::before { content: "⏰"; }

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
  font-size: 1rem;
  color: rgba(255,255,255,0.9);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Contributions Chart */
.contributions-chart {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255,255,255,0.2);
}

.chart-header {
  text-align: center;
  margin-bottom: 2rem;
}

.chart-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin: 0 0 0.5rem 0;
}

.chart-subtitle {
  color: rgba(255,255,255,0.8);
  font-size: 0.875rem;
  margin: 0;
}

.chart-container {
  position: relative;
  height: 300px;
  margin: 0 auto;
  background: rgba(255,255,255,0.1);
  border-radius: 0.5rem;
  padding: 1rem;
}

.projects-section {
  margin: 3rem 0;
}

.gh-content .section-header {
  text-align: center;
  position: relative;
  margin: 3rem 0 2rem 0;
  font-weight: 600;
  font-size: 2.5rem;
}

.section-header::before,
.section-header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: calc(50% - 120px);
  height: 1px;
  background: #e2e8f0;
}

.section-header::before {
  left: 0;
}

.section-header::after {
  right: 0;
}

.section-title {
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 2rem 0;
}

.project-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.project-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.project-logo {
  width: 48px;
  height: 48px;
  border-radius: 0.5rem;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.25rem;
  color: #2563eb;
}

.project-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}

.project-info h3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.project-meta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.project-badge {
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-active {
  background: #dcfce7;
  color: #166534;
}

.badge-discontinued {
  background: #fef3c7;
  color: #92400e;
}

.badge-framework {
  background: #dbeafe;
  color: #1e40af;
}

.badge-library {
  background: #f3e8ff;
  color: #7c3aed;
}

.badge-organization {
  background: #fce7f3;
  color: #be185d;
}

.badge-role {
  background: #f0f9ff;
  color: #0c4a6e;
  border: 1px solid #0ea5e9;
}

.project-description {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.project-links {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  color: #495057;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.project-link:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
  text-decoration: none;
}

.highlights-section {
  margin-top: 1rem;
  padding-top: 1rem;
}

.highlights-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}

.highlights-list li {
  color: #495057;
  position: relative;
  padding-left: 2.5rem;
}

.highlights-list li:before {
  content: "✓";
  position: absolute;
  left: -0.5rem;
  color: #10b981;
  font-weight: bold;
}

.highlighted-repos {
  margin-top: 1rem;
  padding-top: 1rem;
}

.highlighted-repos h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #374151;
}

.repos-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.repo-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.repo-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.repo-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.repo-name {
  font-weight: 600;
  color: #1e293b;
  text-decoration: none;
  font-size: 18px;
}

.repo-name:hover {
  color: #3b82f6;
  text-decoration: none;
}

.repo-stats {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.commit-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.commit-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.repo-description {
  font-size: 15px;
  color: #475569;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.repo-links {
  margin-top: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.repo-link {
  font-size: 14px;
  padding: 0.5rem 1rem;
  background: #e2e8f0;
  color: #475569;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
  font-weight: 500;
}

.repo-link:hover {
  background: #cbd5e1;
  text-decoration: none;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .project-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .project-links {
    flex-direction: column;
  }
  .chart-container {
    height: 250px;
  }
  .section-header::before,
  .section-header::after {
    width: calc(50% - 80px);
  }

  /* Mobile styles for CTA */
  .sponsors-cta {
    padding: 2rem 1.5rem;
    margin: 1.5rem 0 2rem 0;
  }

  .cta-icon {
    font-size: 2.5rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-description {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
